package com.bukuwarung;

import static com.bukuwarung.FirebaseExtKt.initializeFirebase;
import static com.bukuwarung.activities.notification.worker.PushFCMTokenWorkerKt.scheduleWeeklyFCMTokenPush;
import static com.bukuwarung.managers.BootReceiver.BOOT_RECEIVER_NAME;
import static com.bukuwarung.session.User.DEF_USER_STR;

import android.content.Context;
import android.content.Intent;
import android.location.LocationManager;
import android.os.StrictMode;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.ProcessLifecycleOwner;
import androidx.multidex.MultiDex;
import androidx.work.Configuration;

import com.bukuwarung.analytics.AppAnalytics;
import com.zoho.livechat.android.MobilistenActivityLifecycleCallbacks;
import com.bukuwarung.bluetooth_printer.BukuPrinter;
import com.bukuwarung.data.app.api.AppRepository;
import com.bukuwarung.database.entity.UserConfig;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.FirebaseRepository;
import com.bukuwarung.di.AppScope;
import com.bukuwarung.enums.Language;
import com.bukuwarung.location.LocationUtil;
import com.bukuwarung.location.repository.LocationRepository;
import com.bukuwarung.managers.BootReceiver;
import com.bukuwarung.managers.lifecycle.AppLifecycleManager;
import com.bukuwarung.managers.local_notification.LocalNotificationManager;
import com.bukuwarung.neuro.api.Neuro;
import com.bukuwarung.neuro.api.SignalHandler;
import com.bukuwarung.payments.utils.BukuWorkerFactory;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.stealth.Stealth;
import com.bukuwarung.utils.PermissonUtil;
import com.bukuwarung.utils.RemoteConfigUtils;
import com.bukuwarung.utils.TransactionUtil;
import com.bukuwarung.utils.Utilities;
import com.bukuwarung.utils.Utility;
import com.bureau.base.Environment;
import com.bureau.base.models.BureauConfig;
import com.bureau.devicefingerprint.BureauAPI;
import com.facebook.FacebookSdk;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.FirebaseFirestoreSettings;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.messaging.FirebaseMessaging;
import com.gu.toolargetool.Formatter;
import com.gu.toolargetool.Logger;
import com.gu.toolargetool.TooLargeTool;
import com.zoho.salesiqembed.ZohoSalesIQ;

import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Date;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.inject.Inject;
import javax.inject.Provider;

import dagger.hilt.android.HiltAndroidApp;
import kotlin.Unit;
import kotlinx.coroutines.CoroutineScope;

@HiltAndroidApp
@Stealth
public class Application extends android.app.Application implements Configuration.Provider {

    @Inject
    BukuWorkerFactory bukuWorkerFactory;

    private static Context context;
    AppLifecycleManager appLifecycleManager;

    @Inject
    Provider<Neuro> neuro;
    @Inject
    Provider<Set<SignalHandler>> signalHandlers;

    @Inject
    CoroutineMediator coroutineMediator;
    @Inject
    @AppScope
    CoroutineScope appScope;
    @Inject
    Provider<AppRepository> appRepository;
    @Inject
    Provider<Formatter> tooLargeFormatter;
    @Inject
    Provider<Logger> tooLargeLogger;



    @Override
    public void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    public void onCreate() {
        MobilistenActivityLifecycleCallbacks.register(this);
        super.onCreate();
        FacebookSdk.sdkInitialize(this);
        initFirebase();
//        CommonExceptionHandler.Companion.initialize(this, BukuCrashingActivity.class);
        context = getApplicationContext();
        AppAnalytics.initialize(this);
        Log.d("Application", "loadConfig started");
        initTooLargeLogging();
        ExecutorService executorService = Executors.newCachedThreadPool();
        executorService.execute(() -> {
            RemoteConfigUtils.INSTANCE.init();
            SetupManager.getInstance().setSessionSync(false);
            if(BuildConfig.MULTI_LANGUAGE_ENABLED) {
                SessionManager.getInstance().setAppLanguage(SessionManager.getInstance().getAppLanguage());
            } else {
                SessionManager.getInstance().setAppLanguage(Language.DEFAULT.getLangCd());
            }
            BusinessRepository.getInstance(this).loadAppConfig(this);
            Log.d("Application", "bukuwarung started");
            BusinessRepository.getInstance(this).getNotifications();
            BusinessRepository.getInstance(this).getUserNotifications();

            neuro.get().register(new ArrayList<>(signalHandlers.get()));
        });

        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());

        SessionManager.getInstance().getFcmDeviceId();
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(new OnCompleteListener<String>() {
                    @Override
                    public void onComplete(@NonNull Task<String> task) {
                        if (!task.isSuccessful()) {
                            Log.w("TAG", "Fetching FCM registration token failed", task.getException());
                            return;
                        }

                        // Get new FCM registration token
                        String token = task.getResult();
                        Log.v("TAG", "token: "+token);
                        ZohoSalesIQ.Notification.enablePush(token, true);
                    }
                });

        if (User.getUserId() != null && SessionManager.getInstance().isLoggedIn()) {
            loadUserConfig();

            try {
                // location worker
                LocationManager locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
                if ((locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER))
                        && PermissonUtil.hasLocationPermission()) {
                    if (!LocationRepository.Companion.getInstance().getHasSaveTodaysLocation()) {
                        Log.d("LocationStoreWorker", "storing location");
                        storeLocation();
                    }else{
                        Log.d("LocationStoreWorker", "Location already stored");
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }


        SessionManager.getInstance().setRegisteredSessionDevice(false);
//        if (SessionManager.getInstance().pinlockEnabled() && SessionManager.getInstance().isLoggedIn()) {
//            BukuLockManager.Companion.enablePinLock(this);
//            // we want to ask for pin lock every time app starts.
//            BukuLockManager.Companion.expirePinLock();
//        }

        try {
            appLifecycleManager = new AppLifecycleManager();
            ProcessLifecycleOwner.get().getLifecycle().addObserver(appLifecycleManager);
        } catch (Exception ex) {
            // if exception catched, we wont lock app on app resume after background
            ex.printStackTrace();
        }
        FeaturePrefManager.getInstance().setFinishedCstTourMode(false);
        try {
            if (!Utility.isBlank(User.getUserId())) {
                AppAnalytics.setUserAttr();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        LocalNotificationManager localNotificationManager = new LocalNotificationManager(
                new WeakReference<>(context)
        );
        localNotificationManager.start(null);
        startAlarm();
        BukuPrinter.INSTANCE.init(context);
        scheduleWeeklyFCMTokenPush(this);
        initialiseBureau();
    }

    private void initialiseBureau() {
        Environment environment = null;
        if (BuildConfig.FLAVOR.equalsIgnoreCase("prod")) environment = Environment.ENV_PRODUCTION;
        else environment = Environment.ENV_SANDBOX;

        BureauAPI.INSTANCE.init(
                new BureauConfig(
                        BukuWarungKeys.INSTANCE.getBureauClientId(),
                        environment,
                        this
            )
        );
    }

    private void initFirebase() {
        initializeFirebase(this);
        boolean isMainProcess = Utilities.INSTANCE.isMainProcess(this);

        FirebaseFirestoreSettings settings = new FirebaseFirestoreSettings.Builder()
                .setPersistenceEnabled(isMainProcess)
                .build();
        FirebaseFirestore.getInstance().setFirestoreSettings(settings);
    }

    private void initTooLargeLogging() {
        appRepository.get().fetchTooLargeEnabled(coroutineMediator.onResult(appScope, (enabled, t) -> {
            if (enabled) {
                TooLargeTool.startLogging(this, tooLargeFormatter.get(), tooLargeLogger.get());
            }
            return Unit.INSTANCE;
        }));
    }

    private boolean hasRunBroadcastToday() {
        long broadcastRun = SessionManager.getInstance().getDailyBroadcastRun();
        if (broadcastRun == -1L) return false;
        return new Date().getTime() - broadcastRun < TimeUnit.DAYS.toMillis(1);
    }

    private void startAlarm() {
        if (!hasRunBroadcastToday() && !DEF_USER_STR.equals(SessionManager.getInstance().getUserId())) {
            Intent i = new Intent(context, BootReceiver.class);
            i.setAction(BOOT_RECEIVER_NAME);
            sendBroadcast(i);
        }
    }



    public static Context getAppContext() {
        return context;
    }

    private void loadUserConfig() {
        try {
            final FirebaseFirestore mFirestore = FirebaseFirestore.getInstance();
            mFirestore.collection("app_user_config").whereEqualTo("userId", User.getUserId()).get()
                    .addOnCompleteListener(task -> {
                        if (task.isSuccessful()) {
                            int itemCount = task.getResult().size();
                            if (itemCount > 0) {
                                for (QueryDocumentSnapshot doc : task.getResult()) {
                                    try {

                                        /*
                                          old config doesn't contain tnc related attributes
                                          thus it needs to be updated when user updated the app
                                          */
                                        if(doc.get("hasAgreedTnc") == null){
                                            FirebaseRepository.getInstance(context).createInitialUserConfig();
                                        }

                                        UserConfig userConfig = doc.toObject(UserConfig.class);
                                        if (userConfig.lastBackup < 0) {
                                            Thread mThread = new Thread() {
                                                @Override
                                                public void run() {
                                                    TransactionUtil.backupAllTransactions();
                                                }
                                            };
                                            mThread.start();
                                        }
                                        if (userConfig.lastRestore < 0) {
                                            Thread mThread = new Thread() {
                                                @Override
                                                public void run() {
                                                    TransactionUtil.restoreAllTransaction();
                                                }
                                            };
                                            mThread.start();
                                        }

                                        SessionManager.getInstance().setHasAgreedTnC(userConfig.hasAgreedTnc);

                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        FirebaseCrashlytics.getInstance().recordException(e);
                                    }
                                }
                            } else {
                                FirebaseRepository.getInstance(Application.getAppContext()).createInitialUserConfig();
                            }
                        }
                    });
        } catch (Exception e) {
            FirebaseCrashlytics.getInstance().recordException(e);
        }
    }

    private void storeLocation() {
        LocationUtil.INSTANCE.getLocation(this, "startup");
    }

    @NonNull
    @NotNull
    @Override
    public Configuration getWorkManagerConfiguration() {
        return new Configuration.Builder()
                .setWorkerFactory(bukuWorkerFactory)
                .build();
    }


}
