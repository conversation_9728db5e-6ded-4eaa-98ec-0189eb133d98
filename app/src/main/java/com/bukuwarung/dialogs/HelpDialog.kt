package com.bukuwarung.dialogs

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.base.BaseDialogType
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.hideView
import com.zoho.salesiqembed.ZohoSalesIQ
import com.bukuwarung.databinding.DialogHelpBinding

class HelpDialog(context: Context) : BaseDialog(context, BaseDialogType.POPUP) {

    private lateinit var binding: DialogHelpBinding

    override fun getResId(): Int = 0
    val phone: String by lazy { RemoteConfigUtils.getCustomerCarePhoneNumber() }
    private var bookEntity: BookEntity? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        bookEntity =
            BusinessRepository.getInstance(context)?.getBusinessByIdSync(User.getBusinessId())
        setUseFullWidth(false)
        setCancellable(true)
        val minWidth = (context.resources.displayMetrics.widthPixels * 0.90).toInt()
        setMinWidth(minWidth)
        super.onCreate(savedInstanceState)
        binding = DialogHelpBinding.inflate(layoutInflater)
        setupViewBinding(binding.root)
        setUpView()
    }

    private fun setUpView() {
        binding.cvHelpChat.setOnClickListener {
            if (RemoteConfigUtils.showZohodeskHelp()) {
                try {
                    Log.d("HelpDialog", "Attempting to start Zoho chat")

                    // Set visitor information before starting chat
                    ZohoSalesIQ.Visitor.setContactNumber(bookEntity?.businessPhone)
                    bookEntity?.businessEmail?.let {
                        ZohoSalesIQ.Visitor.setEmail(it)
                    } ?: kotlin.run {
                        ZohoSalesIQ.Visitor.setEmail(bookEntity?.businessPhone.plus("@merchant.com"))
                    }
                    ZohoSalesIQ.Visitor.addInfo(
                        "Merchant Tier",
                        SessionManager.getInstance().loyaltyTierName
                    )
                    bookEntity?.businessOwnerName?.let {
                        ZohoSalesIQ.Visitor.setName(it)
                    } ?: kotlin.run {
                        ZohoSalesIQ.Visitor.setName("No Name")
                    }

                    // Try to show existing conversations first, or start new chat
                    try {
                        // First try to show existing conversations
                        ZohoSalesIQ.present(ZohoSalesIQ.Tab.Conversations)
                        Log.d("HelpDialog", "Showing existing Zoho conversations")
                    } catch (e: Exception) {
                        Log.d("HelpDialog", "No existing conversations, starting new chat")
                        // If no existing conversations, start a new chat
                        ZohoSalesIQ.Chat.start("Halo, saya butuh bantuan", null, null) { result ->
                            if (result.isSuccess) {
                                Log.d("HelpDialog", "Zoho chat started successfully")
                            } else {
                                Log.e("HelpDialog", "Failed to start Zoho chat: ${result.error?.message}")
                                // If parallel conversations are disabled, try to show conversations tab
                                if (result.error?.message?.contains("Parallel conversations") == true) {
                                    Log.d("HelpDialog", "Parallel conversations disabled, showing conversations tab")
                                    ZohoSalesIQ.present(ZohoSalesIQ.Tab.Conversations)
                                }
                            }
                        }
                    }

                    Log.d("HelpDialog", "Zoho chat start method called")
                } catch (e: Exception) {
                    Log.e("HelpDialog", "Error opening Zoho chat: ${e.message}", e)
                }
            } else {
                context.startActivity(WebviewActivity.createIntent(context,"https://v2.zopim.com/widget/popout.html?key=j42RhpyMIqP2ZYEYUZGhHKuodHW13WiO", "Help Center"))
            }
            dismiss()
        }

        if (RemoteConfigUtils.showTelephoneHelp()) {
            binding.cvHelpCall.setOnClickListener {
                val intent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:$phone"))
                context.startActivity(intent)
                dismiss()
            }
        } else {
            binding.cvHelpCall.hideView()
        }

        binding.ivHelpClose.setOnClickListener {
            dismiss()
        }

    }
}