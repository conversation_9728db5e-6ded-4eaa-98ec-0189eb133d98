package com.bukuwarung.analytics;

import static com.facebook.FacebookSdk.getApplicationContext;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.location.Location;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.amplitude.api.Amplitude;
import com.amplitude.api.AmplitudeClient;
import com.appsflyer.AppsFlyerLib;
import com.bukuwarung.Application;
import com.bukuwarung.BuildConfig;
import com.bukuwarung.BukuWarungKeys;
import com.bukuwarung.R;
import com.bukuwarung.analytics.core.PayloadBuilder;
import com.bukuwarung.analytics.core.PropertiesEvent;
import com.bukuwarung.constants.AnalyticsConst;
import com.bukuwarung.constants.AppConst;
import com.bukuwarung.database.entity.BookEntity;
import com.bukuwarung.database.repository.BusinessRepository;
import com.bukuwarung.database.repository.ReferralRepository;
import com.bukuwarung.payments.constants.KycTier;
import com.bukuwarung.payments.pref.PaymentPrefManager;
import com.bukuwarung.preference.AppConfigManager;
import com.bukuwarung.preference.FeaturePrefManager;
import com.bukuwarung.preference.ReferralPrefManager;
import com.bukuwarung.session.SessionManager;
import com.bukuwarung.session.User;
import com.bukuwarung.setup.SetupManager;
import com.bukuwarung.utils.Utilities;
import com.bukuwarung.utils.Utility;
import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.google.android.gms.common.GooglePlayServicesNotAvailableException;
import com.google.android.gms.common.GooglePlayServicesRepairableException;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.mixpanel.android.mpmetrics.MixpanelAPI;
import com.zoho.livechat.android.MobilistenActivityLifecycleCallbacks;
import com.zoho.salesiqembed.ZohoSalesIQ;
import com.zoho.salesiqembed.models.SalesIQConfiguration;

import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;


import kotlin.Pair;

public final class AppAnalytics {


    //event name
    public static final String EVENT_REGISTRATION_ASK_OTP = "registration_ask_otp";

    //status value
    public static final String STATUS_FAIL = "fail";

    //detail value
    public static final String DETAIL_NO_NETWORK = "No network";

    private static final Map<String, Object> EMPTY_AF_PROP = new HashMap();
    public static AmplitudeClient amplitude;
    public static MixpanelAPI mixpanel;
    public static AppsFlyerLib appsFlyer;
    public static FirebaseAnalytics firebaseAnalytics;
    private static FirebaseCrashlytics firebaseCrashlytics;

    public static void initialize(Application application) {
        enableLogging();

        //enableLogging();
        AppsFlyerLib.getInstance().init(AppConfigManager.getInstance().getAfApi(), new ConversionListerner(), application.getApplicationContext());
        AppsFlyerLib.getInstance().start(application);
        if(AppConfigManager.getInstance().enableNewAmplitudeProject()) {
            AppConfigManager.getInstance().setUsingNewAmpProject(true);
            Amplitude.getInstance().initialize(application, BukuWarungKeys.INSTANCE.getAmpApi()).enableForegroundTracking(application);
            updateAmplitudeUserId();
        }else{
            AppConfigManager.getInstance().setUsingNewAmpProject(false);
            Amplitude.getInstance().initialize(application, BukuWarungKeys.INSTANCE.getOldAmpApi()).enableForegroundTracking(application);
        }

        SalesIQConfiguration configuration = new SalesIQConfiguration.Builder(
                BukuWarungKeys.INSTANCE.getAppKey(),
                BukuWarungKeys.INSTANCE.getAccessKey()
        ).build();

        Log.d("Mobilisten", "Initializing Zoho SalesIQ with AppKey: " + BukuWarungKeys.INSTANCE.getAppKey());
        ZohoSalesIQ.initialize(application, configuration, result -> {
            if (result.isSuccess()) {
                Log.d("Mobilisten", "Zoho SalesIQ initialized successfully");
                ZohoSalesIQ.showLauncher(false);
                // Set custom theme to fix status bar color
                ZohoSalesIQ.setTheme(com.bukuwarung.R.style.Theme_SalesIQ_Base);
            } else {
                // Handle initialization error
                if (result.getError() != null) {
                    Log.e("Mobilisten", "Initialization failed: code: " + result.getError().getCode() +
                          " , message: " + result.getError().getMessage());
                } else {
                    Log.e("Mobilisten", "Initialization failed with unknown error");
                }
            }
        });
        SurvicateAnalytics.initSurvicateAnalytics();
        Amplitude.getInstance().setOptOut(false);
        amplitude = Amplitude.getInstance();
        mixpanel = MixpanelAPI.getInstance(Application.getAppContext(), BukuWarungKeys.INSTANCE.getMixpanelApi(),true);
        appsFlyer = AppsFlyerLib.getInstance();
        firebaseAnalytics = FirebaseAnalytics.getInstance(Application.getAppContext());
        Task<String> task = firebaseAnalytics.getAppInstanceId();
        task.addOnSuccessListener(new OnSuccessListener<String>() {
            @Override
            public void onSuccess(@NonNull @NotNull String s) {
                SessionManager.getInstance().setAppInstanceId(s);
            }
        });
        firebaseCrashlytics = FirebaseCrashlytics.getInstance();
        trackInstallOrUpdate();
    }

    private static void enableLogging() {
        AppsFlyerLib.getInstance().setDebugLog(true);
        Amplitude.getInstance().enableLogging(true);
        Amplitude.getInstance().setLogLevel(2);
    }

    // track event without property to all channels
    public static void trackEvent(String event) {
        trackEvent(event, true, true, true);
    }

    // track event without property to certain channel
    public static void trackEvent(String event, boolean trackMoengage, boolean trackFirebase, boolean trackAppsflyer) {
        try {
            if (trackMoengage) {
                logEvent(event);
                logHanselEvent(event, null);
                if (trackFirebase) trackFirebaseEvent(event);
                if (trackAppsflyer)
                    appsFlyer.logEvent(Application.getAppContext(), event, EMPTY_AF_PROP);
                Log.i("EVENT_TRACK", event);
                }
            } catch(Exception e){
                handleTrackException(event, e);
            }
    }

    // track event with multiple properties to certain channel
    public static void trackEvent(
            String event, PropBuilder propBuilder,
            boolean trackAmplitude, boolean trackCT,
            boolean trackFirebase, boolean trackAppsflyer
    ) {
        try {
            logHanselEvent(event, propBuilder.buildAmp());
            if (trackAmplitude) logEvent(event, propBuilder.buildAmp());
            if (trackFirebase) trackFirebaseEvent(event, propBuilder.buildBundle());
            if (trackAppsflyer) appsFlyer.logEvent(Application.getAppContext(), event, propBuilder.buildAF());
        } catch (Exception e) {
            handleTrackException(event, e);
        }
    }

    public static void trackAmpEvent(String event) {
        try {
            logHanselEvent(event, null);
            logEvent(event);
        } catch (Exception e) {
            handleTrackException(event, e);
        }
    }

    public static String getAppsflyerId() {
        return AppsFlyerLib.getInstance().getAppsFlyerUID(Application.getAppContext());
    }

    public static void trackAmpEvent(String event, String propStatus, String propDetail) {
        try {
            PropBuilder propBuilder = new PropBuilder();
            propBuilder.put("status", propStatus);
            propBuilder.put("detail", propDetail);
            logHanselEvent(event, propBuilder.buildAmp());
            logEvent(event, propBuilder.buildAmp());
        } catch (Exception e) {
            handleTrackException(event, e);
        }
    }

    // track event with single property
    public static void trackEvent(String event, String propStatus, String propDetail) {
        try {
            PropBuilder propBuilder = new PropBuilder();
            propBuilder.put("status", propStatus);
            propBuilder.put("detail", propDetail);
            logEvent(event, propBuilder.buildAmp());
            appsFlyer.logEvent(Application.getAppContext(), event, propBuilder.buildAF());
            PayloadBuilder payloadBuilder = new PayloadBuilder();
            payloadBuilder.putAttrString("status", propStatus);
            payloadBuilder.putAttrString("detail", propDetail);
            logHanselEvent(event, propBuilder.props);
        } catch (Exception e) {
            handleTrackException(event, e);
        }
    }


    // track event with multiple properties to all channels
    public static void trackEvent(String event, PropBuilder propBuilder) {
            trackEvent(event, propBuilder, true, true, true);
    }

    // track event with multiple properties to certain channel
    public static void trackEvent(String event, PropBuilder propBuilder, boolean trackMoengage, boolean trackFirebase, boolean trackAppsflyer) {
        try {
            logHanselEvent(event, propBuilder.buildAmp());
            logEvent(event, propBuilder.buildAmp());

            if (trackFirebase) trackFirebaseEvent(event, propBuilder.buildBundle());
            if (trackAppsflyer) appsFlyer.logEvent(Application.getAppContext(), event, propBuilder.buildAF());
        } catch (Exception e) {
            handleTrackException(event, e);
        }
    }

    private static void handleTrackException(String event, Exception e) {
        e.printStackTrace();
        firebaseCrashlytics.setCustomKey("last_UI_action", event);
        firebaseCrashlytics.recordException(e);
    }

    public static void trackMoeEvent(String event, PropBuilder propBuilder) {
        try {
            appsFlyer.logEvent(Application.getAppContext(), event, propBuilder.buildAF());
            trackFirebaseEvent(event, propBuilder.buildBundle());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void trackAppsflyerEvent(String event, PropBuilder propBuilder) {
        appsFlyer.logEvent(Application.getAppContext(), event, propBuilder.buildAF());
    }

    public static void trackFirebaseEvent(String event) {
        trackFirebaseEvent(event, new Bundle());
    }

    public static void trackFirebaseEvent(String event, Bundle bundle) {
        firebaseAnalytics.logEvent(event, bundle);
    }

    private static void trackInstallOrUpdate() {
        try {
            SharedPreferences preferences = Application.getAppContext().getSharedPreferences("moengage", Context.MODE_PRIVATE);
            PackageInfo pInfo = Application.getAppContext().getPackageManager().getPackageInfo(Application.getAppContext().getPackageName(), 0);
            if (!preferences.getBoolean("has_sent_install", false)) {
                preferences.edit().putBoolean("has_sent_install", true).apply();
                FeaturePrefManager.getInstance().setCashModuleEnabled(true);
                logEvent("app_install");
                AppConfigManager.getInstance().setEligibleForProfileSetup(true);
                FeaturePrefManager.getInstance().setInstallTimestamp(System.currentTimeMillis());
                FeaturePrefManager.getInstance().setInstallionMode("app_install");
                AppConfigManager.getInstance().setFirstSessionAfterInstall(true);
                FeaturePrefManager.getInstance().setStreaksDialogEnabled(true);
                SetupManager.getInstance().setInstalledVersion(pInfo.versionCode);
                PaymentPrefManager.Companion.getInstance().setKycStatusTracked(false);
            } else if (preferences.getBoolean("has_sent_install", false) && SetupManager.getInstance().getInstalledVersion() < pInfo.versionCode && pInfo.versionCode > 0) {
                FeaturePrefManager.getInstance().setStockEligibilityCheckedAfterUpdate(false);
                FeaturePrefManager.getInstance().setTrackTokenRefreshAfterUpdate(true);
                PropBuilder prop = new PropBuilder();

                prop.put("old_version", SetupManager.getInstance().getInstalledVersion());
                prop.put("new_version", pInfo.versionCode);
                logEvent("user_app_update",prop.buildAmp());
                PaymentPrefManager.Companion.getInstance().setKycStatusTracked(false);
                Utilities.INSTANCE.sendEventsToBackendWithBureau("user_app_update", "app_update",
                        SessionManager.getInstance().getBukuwarungToken(), SessionManager.getInstance().getUUID());

                FeaturePrefManager.getInstance().setInstallionMode("app_update");
                if (!SetupManager.getInstance().getInstallVersionName().equals(pInfo.versionName)) {
                    ReferralRepository.getInstance().newRetroCall();
                }

                SetupManager.getInstance().setInstalledVersion(pInfo.versionCode);
//                setAmplitudeUserProperty("app_version_code", String.valueOf(pInfo.versionCode));
                AppConfigManager.getInstance().setEligibleForProfileSetup(false);
                //api-v4.bukuwarung.com is introduced in latest release, it has to be loaded during app update or user has to restart the app.
                if (!Utility.areEqual(AppConfigManager.getInstance().getOTPApi(), BuildConfig.API_BASE_URL_TWO_FA)) {
                    AppConfigManager.getInstance().setOTPApi(BuildConfig.API_BASE_URL_TWO_FA);
                }
            }

            SetupManager.getInstance().setInstallVersionName(pInfo.versionName);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void logEvent(String eventType) {
        updateAmplitudeUserId();
        if(mixpanel!=null && isValidKycTierUser()) {
            mixpanel.track(eventType);
        }
        amplitude.logEvent(eventType);
    }

    public static boolean isValidKycTierUser(){

        if(!PaymentPrefManager.Companion.getInstance().getKycStatusTracked() && PaymentPrefManager.Companion.getInstance().getKycTier().isVerified() && mixpanel!=null) {
            try {
                PackageInfo pInfo = Application.getAppContext().getPackageManager().getPackageInfo(Application.getAppContext().getPackageName(), 0);
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put(AnalyticsConst.KYC_TIER, PaymentPrefManager.Companion.getInstance().getKycTier());
                propBuilder.put("app_version_code", String.valueOf(pInfo.versionCode));
                propBuilder.put(AnalyticsConst.KYC_STATUS, "VERIFIED");
                if(!Utility.isBlank(SessionManager.getInstance().getUUID())) {
                    propBuilder.put("bw_user_id", SessionManager.getInstance().getUUID());
                }
                mixpanel.registerSuperProperties(propBuilder.buildAmp());
                mixpanel.getPeople().set(propBuilder.buildAmp());
                PaymentPrefManager.Companion.getInstance().setKycStatusTracked(true);
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        return PaymentPrefManager.Companion.getInstance().getKycTier().isVerified();
    }

    public static void logEvent(String eventType, JSONObject eventProperties) {
        updateAmplitudeUserId();
        if(isValidKycTierUser() && mixpanel!=null) {
            mixpanel.track(eventType, eventProperties);
        }
        amplitude.logEvent(eventType,eventProperties);

    }

    private static void updateAmplitudeUserId(){
        if (SessionManager.getInstance().getUserId() == null || User.getUserId().equals(User.DEF_USER_STR) || SessionManager.getInstance().isGuestUser()) {
            if(Utility.canUpdateExistingAmplitudeId(Amplitude.getInstance().getUserId()) && AppConfigManager.getInstance().isUsingNewAmpProject()) {
                Amplitude.getInstance().setUserId(SessionManager.getInstance().getUUID());
                if(mixpanel!=null && isValidKycTierUser()) {
                    mixpanel.identify(SessionManager.getInstance().getUUID(), true);
                    setSuperUserProperty("bw_user_id",SessionManager.getInstance().getUUID());
                }
            }
            return;
        }
        if(AppConfigManager.getInstance().isUsingNewAmpProject()){
            if(!Utility.isBlank(SessionManager.getInstance().getUUID()) || Utility.canUpdateExistingAmplitudeId(Amplitude.getInstance().getUserId())) {
                Amplitude.getInstance().setUserId(SessionManager.getInstance().getUUID());
                if(mixpanel!=null && isValidKycTierUser()) {
                    mixpanel.identify(SessionManager.getInstance().getUUID(),true);
                    setSuperUserProperty("bw_user_id",SessionManager.getInstance().getUUID());
                }
            }
        }else{
            if(Utility.canUpdateExistingAmplitudeId(Amplitude.getInstance().getUserId())) {
                Amplitude.getInstance().setUserId(SessionManager.getInstance().getCountryCode() + "" + User.getUserId());
                if(mixpanel!=null && isValidKycTierUser()) {
                    mixpanel.identify(SessionManager.getInstance().getCountryCode() + "" + User.getUserId(), true);
                    setSuperUserProperty("bw_user_id",SessionManager.getInstance().getUUID());
                }
            }
        }
    }

    public static void setUserAttr() {
        try {
            if (SessionManager.getInstance().getUserId() != null && !User.getUserId().equals(User.DEF_USER_STR) && !SessionManager.getInstance().isGuestUser()) {
                updateAmplitudeUserId();
                String phoneNumber = SessionManager.getInstance().getCountryCode() + "" + User.getUserId();
                firebaseCrashlytics.setUserId(phoneNumber);
                AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
                propBuilder.put("Phone", phoneNumber);

                String identity = SessionManager.getInstance().getUUID();
                if (TextUtils.isEmpty(identity)) {
                    SessionManager.getInstance().forceLogout();
                    return;
                }

                propBuilder.put("Identity", identity);
                propBuilder.put("phoneNumber", phoneNumber);
                propBuilder.put("MSG-email", true);
                propBuilder.put("MSG-push", true);
                propBuilder.put("MSG-sms", true);
                propBuilder.put("MSG-whatsapp", true);

                if (!TextUtils.isEmpty(phoneNumber)) {
                    Map<String, Object> profileUpdate = new HashMap<>();
                    profileUpdate.put("Phone", phoneNumber);
                    profileUpdate.put("phoneNumber", phoneNumber);
                }
            } else if (SessionManager.getInstance().isGuestUser()) {
                appsFlyer.setCustomerUserId(User.getUserId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            String androidId = Settings.Secure.getString(Application.getAppContext().getContentResolver(), Settings.Secure.ANDROID_ID);
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            if (!AppConst.PRODUCTION_FLAVOR.equals(BuildConfig.FLAVOR)) {
                propBuilder.put("android_id", androidId);
                propBuilder.put("phone",User.getUserId());
            }
            if (SessionManager.getInstance().isGuestUser()) {
                propBuilder.put(AnalyticsConst.IS_GUEST, "true");
                propBuilder.put(AnalyticsConst.GUEST_USER_ID, User.getUserId());
            } else {
                propBuilder.put(AnalyticsConst.IS_GUEST, "false");
            }
            propBuilder.put("app_config", Utility.getConfigCode());
            AppAnalytics.setUserProperties(propBuilder.buildAmp());
            if(!Utility.isBlank(androidId)){
                SessionManager.getInstance().setAndroidId(androidId);
            }
            appsFlyer.setCollectAndroidID(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            HashMap<String, Object> guestProp = new HashMap<>();
            guestProp.put(AnalyticsConst.GUEST_USER_ID, User.getUserId());
            guestProp.put(AnalyticsConst.IS_GUEST, SessionManager.getInstance().isGuestUser());
            appsFlyer.setAdditionalData(guestProp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (!Utility.isBlank(ReferralPrefManager.getInstance().getMyReferalCode())) {
                setUserProperty("user_referral_code", ReferralPrefManager.getInstance().getMyReferalCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void setUserProperty(@NotNull String propName, @NotNull String code) {
        try {

            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put(propName, code);
            amplitude.setUserProperties(propBuilder.buildAmp());
            if(isValidKycTierUser() & mixpanel!=null) {
                mixpanel.getPeople().set(propBuilder.buildAmp());
            }
        } catch (Exception e) {

        }
    }

    public static void setUserProperty(@NotNull String propName, @NotNull Object code) {
        try {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put(propName, code);
            amplitude.setUserProperties(propBuilder.buildAmp());
            if(isValidKycTierUser() & mixpanel!=null) {
                mixpanel.getPeople().set(propBuilder.buildAmp());
            }
        } catch (Exception e) {

        }
    }

    public static void setSuperUserProperty(@NotNull String propName, @NotNull Object code) {
        try {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put(propName, code);
            if(isValidKycTierUser() && mixpanel!=null) {
                mixpanel.registerSuperProperties(propBuilder.buildAmp());
            }
        } catch (Exception e) {

        }
    }

    public static void setAmplitudeUserProperty(@NotNull String propName, @NotNull String code) {
        try {

            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put(propName, code);
            if(isValidKycTierUser() & mixpanel!=null) {
                mixpanel.getPeople().set(propBuilder.buildAmp());
            }
            amplitude.setUserProperties(propBuilder.buildAmp());
        } catch (Exception e) {

        }
    }

    public static synchronized String getAdId(Context context) {

        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.KITKAT) {
            return "";
        }

        AdvertisingIdClient.Info idInfo = null;
        try {
            idInfo = AdvertisingIdClient.getAdvertisingIdInfo(context);
        } catch (GooglePlayServicesNotAvailableException e) {
            e.printStackTrace();
        } catch (GooglePlayServicesRepairableException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        String advertId = null;
        try {
            advertId = idInfo.getId();
            SessionManager.getInstance().setAdvertisingId(advertId);
        } catch (NullPointerException e) {
            e.printStackTrace();
        }

        return advertId;
    }

    public static void trackUserSignUp() {
        try {
            AppAnalytics.PropBuilder propBuilder = new AppAnalytics.PropBuilder();
            propBuilder.put("signedUpOn", new Date());
            propBuilder.put("DEVICE_MANUFACTURER", Build.MANUFACTURER);
            propBuilder.put("DEVICE_MODEL", Build.MODEL);
        } catch (Exception e) {

        }
    }

    public static void setUserBusinessAttr() {
        try {
            BookEntity book = BusinessRepository.getInstance(Application.getAppContext()).getBusinessByIdSync(User.getBusinessId());
            setUserProperty("business_id", book.bookId);
            setUserProperty("business_category", book.bookTypeName);
        } catch (Exception e) {
        }
    }

    public static void addExtraEventProps(String eventProps, PropBuilder propBuilder) {
        if (eventProps != null) {
            JsonObject propsJson = new Gson().fromJson(eventProps, JsonObject.class);
            if (propsJson != null) {
                for (Map.Entry<String, JsonElement> entry : propsJson.entrySet()) {
                    propBuilder.put(entry.getKey(), entry.getValue());
                }
            }
        }
    }

    public static void resetUserDetails() {
        Amplitude.getInstance().setUserId(null);
    }

    public static void setUserProperties(JSONObject properties) {
        if(isValidKycTierUser() & mixpanel!=null) {
            mixpanel.getPeople().set(properties);
        }
    }

    public static class PropBuilder {
        HashMap<String, Object> propMap = new HashMap<>();
        JSONObject props = new JSONObject();
        Bundle bundle = new Bundle();
        PropertiesEvent moProperties = new PropertiesEvent();

        public PropBuilder put(String str, Object str2) {
            try {
                this.props.put(str, str2);
                this.propMap.put(str, str2);
                moProperties.addAttribute(str, str2);
                if (str2 instanceof String) {
                    this.bundle.putString(str, (String) str2);
                } else if (str2 instanceof Integer) {
                    this.bundle.putInt(str, (Integer) str2);
                } else if (str2 instanceof Long) {
                    this.bundle.putLong(str, (Long) str2);
                } else if (str2 instanceof Double) {
                    this.bundle.putDouble(str, (Double) str2);
                } else if (str2 instanceof Boolean) {
                    this.bundle.putBoolean(str, (Boolean) str2);
                }
            } catch (Exception e) {
                firebaseCrashlytics.recordException(e);
            }
            return this;
        }

        public PropBuilder addProperty(Pair<String, Object> prop) {
            try {
                props.put(prop.getFirst(), prop.getSecond());
                propMap.put(prop.getFirst(), prop.getSecond());
                moProperties.addAttribute(prop.getFirst(), prop.getSecond());

                if (prop.getSecond() instanceof String) {
                    this.bundle.putString(prop.getFirst(), (String) prop.getSecond());
                } else if (prop.getSecond() instanceof Integer) {
                    this.bundle.putInt(prop.getFirst(), (Integer) prop.getSecond());
                } else if (prop.getSecond() instanceof Long) {
                    this.bundle.putLong(prop.getFirst(), (Long) prop.getSecond());
                } else if (prop.getSecond() instanceof Double) {
                    this.bundle.putDouble(prop.getFirst(), (Double) prop.getSecond());
                } else if (prop.getSecond() instanceof Boolean) {
                    this.bundle.putBoolean(prop.getFirst(), (Boolean) prop.getSecond());
                }
            } catch (Exception ex) {
                firebaseCrashlytics.recordException(ex);
            }

            return this;
        }

        public PropertiesEvent buildMoengage() {
            return moProperties;
        }

        public PropBuilder addEntryPointProperty(String entryPoint) {
            put(AnalyticsConst.ENTRY_POINT2, entryPoint);
            return this;
        }

        public PropBuilder stringToProp(String propJson) {
            try {
                Map<String, Object> propMap = new Gson().fromJson(
                        propJson, new TypeToken<HashMap<String, Object>>() {
                        }.getType()
                );
                for (Map.Entry<String, Object> entry : propMap.entrySet()) {
                    put(entry.getKey(), entry.getValue());
                }
            } catch (Exception ex) {
                firebaseCrashlytics.recordException(ex);
            }

            return this;
        }

        public PropertiesEvent buildCT() {

            return moProperties;
        }

        public JSONObject buildAmp() {
            return this.props;
        }


        public Map<String, Object> buildAF() {
            return propMap;
        }

        public Bundle buildBundle() {
            return bundle;
        }

    }

    public static void logHanselEvent(String eventName, JSONObject properties) {

        if (properties == null) {
            properties = new JSONObject();
        }
        HashMap<String, Object> propertiesMap = new HashMap<>();
        for (Iterator<String> it = properties.keys(); it.hasNext(); ) {
            String key = it.next();
            propertiesMap.put(key, properties.opt(key));
        }
        //Please pass the string "amp" for vendor if you are using Amplitude to track the event.

        //amplitudeClient.logEvent(eventName, properties);
    }

}
